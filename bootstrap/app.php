<?php

$_RTP = __DIR__ . '/../';

require_once $_RTP . 'vendor/autoload.php';

(new Dotenv\Dotenv($_RTP))->load();

use Carbon\Carbon;

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| Here we will load the environment and create the application instance
| that serves as the central piece of this framework. We'll use this
| application as an "IoC" container and router for this framework.
|
 */

$app = new Laravel\Lumen\Application(
    realpath($_RTP)
);

$app->withFacades();
$app->withEloquent();

/*
|--------------------------------------------------------------------------
| Add Config
|--------------------------------------------------------------------------
 */
$app->configure('api');
$app->configure('app');
$app->configure('cache');
$app->configure('cors');
$app->configure('excel');
$app->configure('database');
$app->configure('express_company');
$app->configure('common_templates');
$app->configure('dy_service_attributes');
$app->configure('jd_service_attributes');
$app->configure('ks_service_attributes');
$app->configure('wxsp_service_attributes');
$app->configure('custom_area_contents');
$app->configure('socialite');
$app->configure('waybill');
$app->configure('taobaotop');
$app->configure('sms');
$app->configure('all_print_app');
$app->configure('logging');
$app->configure('sentry');
$app->configure('filesystems');
$app->configure('xhprof');
$app->configure('platform_api');
$app->configure('platform_api_appid');
$app->configure('jd_cloud');
$app->configure('subscription');
$app->configure('pay');
$app->configure('mail');
$app->configure('uniprint');

Carbon::setLocale('zh');
// echo Carbon::now()->diffInDays(Carbon::parse('2020-07-08 18:22:05'));
// exit;
/*
|--------------------------------------------------------------------------
| Register Container Bindings
|--------------------------------------------------------------------------
|
| Now we will register a few bindings in the service container. We will
| register the exception handler and the console kernel. You may add
| your own bindings here if you like or you can make another file.
|
 */

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton('filesystem', function ($app) {
    return $app->loadComponent(
        'filesystems',
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        'filesystem'
    );
});
/*
|--------------------------------------------------------------------------
| The application's global HTTP middleware stack.
|--------------------------------------------------------------------------
|
 */
$app->middleware([
    \App\Http\Middleware\HandleReqId::class,
    \App\Http\Middleware\Waf::class,
    \Barryvdh\Cors\HandleCors::class,
    \App\Http\Middleware\Xhprof::class,
]);
/*
|--------------------------------------------------------------------------
| Register Middleware
|--------------------------------------------------------------------------
|
| Next, we will register the middleware with the application. These can
| be global middleware that run before and after each request into a
| route or middleware that'll be assigned to some specific routes.
|
 */

$app->routeMiddleware([
    'cors'          => \Barryvdh\Cors\HandleCors::class,
    'check.token'   => \App\Http\Middleware\CheckToken::class,
    'hide.privacy'   => \App\Http\Middleware\ThrottleRequests::class,
    'check.sign'   => \App\Http\Middleware\CheckSign::class,
    'check.sign.v3'   => \App\Http\Middleware\CheckSignV3::class,
    'check.sign.version'   => \App\Http\Middleware\CheckSignByVersion::class,
    'check.shop.token' => \App\Http\Middleware\CheckShop::class,
    'check.shop.code' => \App\Http\Middleware\CheckShopCode::class,
    'check.shop.code.and.wp.shop.code' => \App\Http\Middleware\CheckShopCodeAndWpShopCode::class,
    'check.api.shop.bind' => \App\Http\Middleware\CheckApiShopBind::class,
    'logger' => \App\Http\Middleware\Logger::class,
    'api.log' => \App\Http\Middleware\OpenApiLog::class,
    'rate.limiter'=>\App\Http\Middleware\RequestRateLimiter::class,
    'appid.rate.limiter'=>\App\Http\Middleware\AppIdRateLimiter::class,
    'check.admin' => \App\Http\Middleware\CheckAdmin::class,
    'appid.ip.whitelist' => \App\Http\Middleware\AppIdIpWhitelist::class,
]);

/**
 * 日志重写机制
 */
//$app->configureMonologUsing(function(Monolog\Logger $monolog) use ($app) {
//    return $monolog->pushHandler(
//        new \Monolog\Handler\RotatingFileHandler($app->storagePath().'/logs/lumen.log')
//    );
//});

/*
|--------------------------------------------------------------------------
| Register Service Providers
|--------------------------------------------------------------------------
|
| Here we will register all of the application's service providers which
| are used to bind services into the container. Service providers are
| totally optional, so you are not required to uncomment this line.
|
 */

$app->register(App\Providers\AppServiceProvider::class);
$app->register(App\Providers\EventServiceProvider::class);
$app->register(Hhxsv5\LaravelS\Illuminate\LaravelSServiceProvider::class);
$app->register(Illuminate\Redis\RedisServiceProvider::class);
$app->register(Barryvdh\Cors\ServiceProvider::class);
$app->register(Maatwebsite\Excel\ExcelServiceProvider::class);
$app->register(FireSoil\TopClient\TopClientServiceProvider::class);
$app->register(Sentry\Laravel\ServiceProvider::class);
$app->register(\Illuminate\Mail\MailServiceProvider::class);

//if (!class_exists('TopClient')) {
//    class_alias('FireSoil\TopClient\Facades\TopClient', 'TopClient');
//}
/*
|--------------------------------------------------------------------------
| Load The Application Routes
|--------------------------------------------------------------------------
|
| Next we will include the routes file so that they can all be added to
| the application. This will provide all of the URLs the application
| can respond to, as well as the controllers that may handle them.
|
 */

$app->router->group([
    'namespace' => 'App\Http\Controllers',
], function ($router) use ($_RTP) {
    require $_RTP . 'routes/api.php';
});

return $app;
