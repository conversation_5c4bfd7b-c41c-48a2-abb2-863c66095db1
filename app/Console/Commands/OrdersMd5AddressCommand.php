<?php


namespace App\Console\Commands;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Jobs\Orders\SynOrderMd5AddressJob;
/**
 * 
 * Class OrdersMd5AddressCommand
 * @package App\Console\Commands
 */
class OrdersMd5AddressCommand extends Command
{
    protected $name = 'command:orders-md5-addrss';
    protected $description = '订单order 表里md5地址加密';


    /**
     * Execute the console command.
     *
     * @return mixed
     * @throws OrderException
     */
    public function handle()
    {
        Order::query()->chunk(500, function ($orders) {
			dispatch(new SynOrderMd5AddressJob($orders));
        });
    }
}
