<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/29
 * Time: 17:42
 */

namespace App\Console\Commands;


use App\Jobs\Orders\DeleteOrderJob;
use App\Models\ApiShopBind;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Order;
use App\Models\Shop;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 清理过期店铺
 * Class ClearExpiredShopCmd
 * @package App\Console\Commands
 */
class ClearExpiredGiftShopData extends AbstractClearOrderCmd
{
    protected $signature = 'clear:expired-gift-shop-data';

    protected $description = '清理礼品网过期店铺的数据';

    public function handle()
    {
        //从api_shop_bind表中查找礼品网的店铺，然后看这些店铺是否过期，过期的话，就清理数据
        //>60天的数据，就清理
       $query=Shop::query()->whereIn('id',function ($query) {
           $query->select('shop_id')->from('api_shop_binds');
       })->where('expire_at', '<', Carbon::today()->subDays(7));

        $query->each(function ($shop) {
            try {
                Log::info('清理店铺',["id"=>$shop->id,"name"=>$shop->name,"expire_at"=>$shop->expire_at]);
                $this->clearOrderData($shop);
            } catch (\Exception $e) {
                Log::error('清理过期的店铺失败', [$shop, $e->getMessage()]);
            }
        }, 100);
    }


}
